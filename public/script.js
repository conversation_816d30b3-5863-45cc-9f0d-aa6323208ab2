// Socket.IO connection
const socket = io();

// DOM elements
const chatMessages = document.getElementById('chatMessages');
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const status = document.getElementById('status');
const typingIndicator = document.getElementById('typingIndicator');

// State
let isWaitingForResponse = false;
let currentAIMessage = null;

// Socket event handlers
socket.on('connect', () => {
    console.log('Connected to server');
    updateStatus('Connected', 'connected');
});

socket.on('disconnect', () => {
    console.log('Disconnected from server');
    updateStatus('Disconnected - Reconnecting...', 'error');
});

socket.on('message_received', (data) => {
    console.log('Message received by server:', data.message);
    showTypingIndicator();
});

socket.on('ai_chunk', (chunk) => {
    handleAIChunk(chunk);
});

socket.on('ai_complete', (data) => {
    console.log('AI response complete:', data);
    hideTypingIndicator();
    isWaitingForResponse = false;
    updateSendButton();
    
    if (data.functionCalls && data.functionCalls.length > 0) {
        addFunctionCallInfo(data.functionCalls);
    }
    
    scrollToBottom();
});

socket.on('ai_error', (error) => {
    console.error('AI Error:', error);
    hideTypingIndicator();
    isWaitingForResponse = false;
    updateSendButton();
    
    addMessage('ai', `❌ Error: ${error.message || 'An error occurred while processing your request.'}`);
    scrollToBottom();
});

socket.on('conversation_reset', () => {
    chatMessages.innerHTML = `
        <div class="message ai">
            <div class="message-content">
                👋 Hello! I'm your NVD AI assistant. I can help you with:
                <br><br>
                • 🔍 Search for specific CVE vulnerabilities
                • 📊 Find vulnerabilities by severity level
                • 🏢 Look up vulnerabilities for specific products
                • 📅 Get recent or date-range based vulnerabilities
                • 🔧 Provide security insights and remediation advice
                <br><br>
                Try asking: "Show me recent critical vulnerabilities" or "Tell me about CVE-2021-44228"
            </div>
        </div>
    `;
    updateStatus('Conversation reset', 'connected');
    setTimeout(() => updateStatus('Connected', 'connected'), 2000);
});

// UI Functions
function updateStatus(message, type = '') {
    status.textContent = message;
    status.className = `status ${type}`;
}

function updateSendButton() {
    sendButton.disabled = isWaitingForResponse;
    sendButton.textContent = isWaitingForResponse ? 'Sending...' : 'Send';
}

function showTypingIndicator() {
    typingIndicator.classList.add('show');
    scrollToBottom();
}

function hideTypingIndicator() {
    typingIndicator.classList.remove('show');
}

function addMessage(sender, content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    // Format content for better display
    if (sender === 'ai') {
        contentDiv.innerHTML = formatAIContent(content);
    } else {
        contentDiv.textContent = content;
    }
    
    messageDiv.appendChild(contentDiv);
    chatMessages.appendChild(messageDiv);
    
    return messageDiv;
}

function formatAIContent(content) {
    // Convert markdown-like formatting to HTML
    let formatted = content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>');
    
    // Format CVE IDs as links (if they exist)
    formatted = formatted.replace(
        /CVE-\d{4}-\d{4,}/g, 
        '<strong style="color: #d32f2f;">$&</strong>'
    );
    
    return formatted;
}

function handleAIChunk(chunk) {
    if (chunk.type === 'content') {
        // Handle streaming content
        if (!currentAIMessage) {
            currentAIMessage = addMessage('ai', '');
        }
        
        const contentDiv = currentAIMessage.querySelector('.message-content');
        const currentContent = contentDiv.getAttribute('data-raw-content') || '';
        const newContent = currentContent + chunk.content;
        
        contentDiv.setAttribute('data-raw-content', newContent);
        contentDiv.innerHTML = formatAIContent(newContent);
        
        scrollToBottom();
    } else if (chunk.type === 'function_call') {
        // Handle function call notifications
        hideTypingIndicator();
        addFunctionCallMessage(chunk.function, chunk.arguments);
        showTypingIndicator();
    }
}

function addFunctionCallMessage(functionName, args) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message ai';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    const functionDiv = document.createElement('div');
    functionDiv.className = 'function-call';
    functionDiv.innerHTML = `
        <strong>🔧 Calling Function:</strong> ${functionName}<br>
        <strong>📋 Parameters:</strong> ${JSON.stringify(args, null, 2)}
    `;
    
    contentDiv.appendChild(functionDiv);
    messageDiv.appendChild(contentDiv);
    chatMessages.appendChild(messageDiv);
    
    scrollToBottom();
}

function addFunctionCallInfo(functionCalls) {
    if (functionCalls.length === 0) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message ai';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    const infoDiv = document.createElement('div');
    infoDiv.className = 'function-call';
    infoDiv.innerHTML = `
        <strong>✅ Completed Functions:</strong><br>
        ${functionCalls.map(fc => `• ${fc.function}`).join('<br>')}
    `;
    
    contentDiv.appendChild(infoDiv);
    messageDiv.appendChild(contentDiv);
    chatMessages.appendChild(messageDiv);
    
    scrollToBottom();
}

function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function sendMessage() {
    const message = messageInput.value.trim();
    if (!message || isWaitingForResponse) return;
    
    // Add user message to chat
    addMessage('user', message);
    
    // Clear input
    messageInput.value = '';
    
    // Update state
    isWaitingForResponse = true;
    currentAIMessage = null;
    updateSendButton();
    
    // Send to server
    socket.emit('user_message', { message });
    
    scrollToBottom();
}

function resetConversation() {
    if (confirm('Are you sure you want to reset the conversation?')) {
        socket.emit('reset_conversation');
    }
}

// Event listeners
messageInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
    }
});

sendButton.addEventListener('click', sendMessage);

// Auto-focus input
messageInput.focus();

// Handle connection errors
socket.on('connect_error', (error) => {
    console.error('Connection error:', error);
    updateStatus('Connection failed', 'error');
});

socket.on('reconnect', (attemptNumber) => {
    console.log('Reconnected after', attemptNumber, 'attempts');
    updateStatus('Reconnected', 'connected');
});

socket.on('reconnect_error', (error) => {
    console.error('Reconnection error:', error);
    updateStatus('Reconnection failed', 'error');
});

// Initialize
updateStatus('Connected', 'connected');

import dotenv from "dotenv";
dotenv.config();

import express from "express";
import { createServer } from "http";
import { Server } from "socket.io";
import path from "path";
import { fileURLToPath } from "url";
import nvdChat from "./utils/nvd-ai-function-calling.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
  },
});

const PORT = process.env.PORT || 3000;

// Serve static files
app.use(express.static(path.join(__dirname, "public")));

// Serve the main HTML file
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "public", "index.html"));
});

// Store conversation history for each socket
const conversations = new Map();

io.on("connection", (socket) => {
  console.log(`User connected: ${socket.id}`);

  // Initialize conversation for this socket
  conversations.set(socket.id, []);

  // Handle user messages
  socket.on("user_message", async (data) => {
    try {
      const { message } = data;
      console.log(`Message from ${socket.id}:`, message);

      // Get conversation history
      const conversationHistory = conversations.get(socket.id) || [];

      // Add user message to history
      const userMessage = { role: "user", content: message };
      conversationHistory.push(userMessage);

      // Update conversation history
      conversations.set(socket.id, conversationHistory);

      // Send acknowledgment that message was received
      socket.emit("message_received", { message });

      // Handle the chat with streaming
      const result = await nvdChat.handleNVDChat(conversationHistory, {
        stream: true,
        onChunk: (chunk) => {
          // Send streaming chunks to the client
          socket.emit("ai_chunk", chunk);
        },
      });

      if (result.success) {
        // Add assistant response to conversation history
        const assistantMessage = {
          role: "assistant",
          content: result.response,
        };
        conversationHistory.push(assistantMessage);
        conversations.set(socket.id, conversationHistory);

        // Send completion signal
        socket.emit("ai_complete", {
          success: true,
          functionCalls: result.functionCalls || [],
          usage: result.usage,
        });
      } else {
        // Send error
        socket.emit("ai_error", {
          error: result.error,
          message: result.response,
        });
      }
    } catch (error) {
      console.error(`Error handling message from ${socket.id}:`, error);
      socket.emit("ai_error", {
        error: error.message,
        message: "An error occurred while processing your request.",
      });
    }
  });

  // Handle conversation reset
  socket.on("reset_conversation", () => {
    conversations.set(socket.id, []);
    socket.emit("conversation_reset");
    console.log(`Conversation reset for ${socket.id}`);
  });

  // Handle disconnect
  socket.on("disconnect", () => {
    console.log(`User disconnected: ${socket.id}`);
    conversations.delete(socket.id);
  });
});

server.listen(PORT, () => {
  console.log(`🚀 NVD AI Chat Server running on http://localhost:${PORT}`);
  console.log(`📡 Socket.IO server ready for connections`);
});

#!/usr/bin/env node

/**
 * Test script to verify the NVD AI function calling fix
 * This script tests the message structure validation
 */

import nvdChat from './utils/nvd-ai-function-calling.js';

async function testNVDChat() {
  console.log('Testing NVD Chat functionality...');
  
  try {
    // Test 1: Simple query that should trigger function calling
    console.log('\n=== Test 1: Simple CVE query ===');
    const result1 = await nvdChat.queryNVD("Tell me about CVE-2021-44228", {
      model: "gpt-4",
      maxTokens: 500,
      stream: false
    });
    
    console.log('Result 1 success:', result1.success);
    if (result1.success) {
      console.log('Response preview:', result1.response?.substring(0, 100) + '...');
      console.log('Function calls made:', result1.functionCalls?.length || 0);
    } else {
      console.log('Error:', result1.error);
    }
    
    // Test 2: Conversation with multiple messages
    console.log('\n=== Test 2: Multi-message conversation ===');
    const messages = [
      { role: "user", content: "Search for log4j vulnerabilities" },
      { role: "assistant", content: "I'll search for log4j vulnerabilities for you." },
      { role: "user", content: "What's the most critical one?" }
    ];
    
    const result2 = await nvdChat.handleNVDChat(messages, {
      model: "gpt-4",
      maxTokens: 500,
      stream: false
    });
    
    console.log('Result 2 success:', result2.success);
    if (result2.success) {
      console.log('Response preview:', result2.response?.substring(0, 100) + '...');
      console.log('Function calls made:', result2.functionCalls?.length || 0);
    } else {
      console.log('Error:', result2.error);
    }
    
    // Test 3: Test streaming functionality
    console.log('\n=== Test 3: Streaming response ===');
    let streamContent = '';
    const result3 = await nvdChat.queryNVD("Get CVE details for CVE-2023-1234", {
      model: "gpt-4",
      maxTokens: 300,
      stream: true,
      onChunk: (chunk) => {
        if (chunk.type === 'content') {
          streamContent += chunk.content;
        } else if (chunk.type === 'function_call') {
          console.log(`Function call: ${chunk.function}`);
        }
      }
    });
    
    console.log('Result 3 success:', result3.success);
    if (result3.success) {
      console.log('Streamed content preview:', streamContent.substring(0, 100) + '...');
      console.log('Function calls made:', result3.functionCalls?.length || 0);
    } else {
      console.log('Error:', result3.error);
    }
    
  } catch (error) {
    console.error('Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testNVDChat().then(() => {
  console.log('\n=== Test completed ===');
}).catch((error) => {
  console.error('Test script failed:', error);
  process.exit(1);
});

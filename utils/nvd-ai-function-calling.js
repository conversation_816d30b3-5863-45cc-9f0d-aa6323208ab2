import { OpenAI } from "openai";
import * as nvdClient from "./nvd-utils.js";
import { env } from "./env.js";

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: env.OPENAI_API_KEY || "dummy-key-for-testing",
});

/**
 * NVD function definitions for OpenAI function calling
 */
const nvdTools = [
  {
    type: "function",
    function: {
      name: "get_cve_by_id",
      description:
        "Get detailed information about a specific CVE vulnerability by its ID",
      parameters: {
        type: "object",
        properties: {
          cveId: {
            type: "string",
            description: "CVE identifier (e.g., 'CVE-2021-44228')",
          },
        },
        required: ["cveId"],
        additionalProperties: false,
      },
    },
  },
  {
    type: "function",
    function: {
      name: "search_cves_by_keyword",
      description: "Search for CVE vulnerabilities using keywords or phrases",
      parameters: {
        type: "object",
        properties: {
          keyword: {
            type: "string",
            description:
              "Search keyword or phrase (e.g., 'log4j', 'remote code execution')",
          },
          exactMatch: {
            type: "boolean",
            description: "Whether to match the exact phrase (default: false)",
            default: false,
          },
          limit: {
            type: "number",
            description: "Maximum number of results to return (default: 10)",
            default: 10,
          },
        },
        required: ["keyword"],
        additionalProperties: false,
      },
    },
  },
  {
    type: "function",
    function: {
      name: "get_cves_by_severity",
      description: "Get CVE vulnerabilities filtered by CVSS severity level",
      parameters: {
        type: "object",
        properties: {
          severity: {
            type: "string",
            enum: ["LOW", "MEDIUM", "HIGH", "CRITICAL"],
            description: "CVSS severity level",
          },
          version: {
            type: "string",
            enum: ["v2", "v3", "v4"],
            description: "CVSS version (default: v3)",
            default: "v3",
          },
          limit: {
            type: "number",
            description: "Maximum number of results to return (default: 10)",
            default: 10,
          },
        },
        required: ["severity"],
        additionalProperties: false,
      },
    },
  },
  {
    type: "function",
    function: {
      name: "get_recent_cves",
      description: "Get recently published CVE vulnerabilities",
      parameters: {
        type: "object",
        properties: {
          days: {
            type: "number",
            description: "Number of days to look back (default: 7)",
            default: 7,
          },
          limit: {
            type: "number",
            description: "Maximum number of results to return (default: 10)",
            default: 10,
          },
        },
        required: [],
        additionalProperties: false,
      },
    },
  },
  {
    type: "function",
    function: {
      name: "get_cves_by_cpe",
      description:
        "Get CVE vulnerabilities associated with a specific product or technology",
      parameters: {
        type: "object",
        properties: {
          cpeName: {
            type: "string",
            description:
              "CPE name string (e.g., 'cpe:2.3:a:apache:log4j:*:*:*:*:*:*:*:*')",
          },
          vulnerableOnly: {
            type: "boolean",
            description:
              "Only return vulnerable configurations (default: false)",
            default: false,
          },
          limit: {
            type: "number",
            description: "Maximum number of results to return (default: 10)",
            default: 10,
          },
        },
        required: ["cpeName"],
        additionalProperties: false,
      },
    },
  },
  {
    type: "function",
    function: {
      name: "get_cves_by_cwe",
      description:
        "Get CVE vulnerabilities by Common Weakness Enumeration (CWE) type",
      parameters: {
        type: "object",
        properties: {
          cweId: {
            type: "string",
            description:
              "CWE identifier (e.g., 'CWE-79' for XSS, 'CWE-89' for SQL injection)",
          },
          limit: {
            type: "number",
            description: "Maximum number of results to return (default: 10)",
            default: 10,
          },
        },
        required: ["cweId"],
        additionalProperties: false,
      },
    },
  },
  {
    type: "function",
    function: {
      name: "get_high_severity_kev_cves",
      description:
        "Get high-severity CVEs that are in CISA's Known Exploited Vulnerabilities catalog",
      parameters: {
        type: "object",
        properties: {
          limit: {
            type: "number",
            description: "Maximum number of results to return (default: 10)",
            default: 10,
          },
        },
        required: [],
        additionalProperties: false,
      },
    },
  },
  {
    type: "function",
    function: {
      name: "get_cves_by_date_range",
      description:
        "Get CVE vulnerabilities published or modified within a specific date range",
      parameters: {
        type: "object",
        properties: {
          startDate: {
            type: "string",
            description:
              "Start date in ISO 8601 format (e.g., '2023-01-01T00:00:00.000Z')",
          },
          endDate: {
            type: "string",
            description:
              "End date in ISO 8601 format (e.g., '2023-12-31T23:59:59.999Z')",
          },
          dateType: {
            type: "string",
            enum: ["published", "modified"],
            description: "Type of date to filter by (default: published)",
            default: "published",
          },
          limit: {
            type: "number",
            description: "Maximum number of results to return (default: 10)",
            default: 10,
          },
        },
        required: ["startDate", "endDate"],
        additionalProperties: false,
      },
    },
  },
  {
    type: "function",
    function: {
      name: "get_cve_change_history",
      description: "Get change history for a specific CVE",
      parameters: {
        type: "object",
        properties: {
          cveId: {
            type: "string",
            description: "CVE identifier to get change history for",
          },
        },
        required: ["cveId"],
        additionalProperties: false,
      },
    },
  },
];

/**
 * Function implementations that call the NVD API
 */
const functionImplementations = {
  async get_cve_by_id({ cveId }) {
    try {
      const result = await nvdClient.getCVEById(cveId);
      if (!result) {
        return {
          success: true,
          data: null,
          message: `CVE ${cveId} not found`,
        };
      }

      // Extract only essential information to reduce token usage
      const cve = result.cve;
      const description =
        cve.descriptions?.find((d) => d.lang === "en")?.value ||
        "No description available";
      const cvssV3 = result.cve.metrics?.cvssMetricV3?.[0]?.cvssData;
      const cvssV2 = result.cve.metrics?.cvssMetricV2?.[0]?.cvssData;

      // Create a compact version with only essential data
      const compactResult = {
        id: cve.id,
        description:
          description.length > 500
            ? description.substring(0, 500) + "..."
            : description,
        published: cve.published,
        lastModified: cve.lastModified,
        severity: cvssV3
          ? {
              score: cvssV3.baseScore,
              severity: cvssV3.baseSeverity,
              vector: cvssV3.vectorString,
            }
          : cvssV2
          ? {
              score: cvssV2.baseScore,
              severity: cvssV2.baseSeverity,
              vector: cvssV2.vectorString,
            }
          : null,
        references: result.cve.references?.slice(0, 3) || [], // Limit to first 3 references
        configurations: result.cve.configurations
          ? "Available (truncated for brevity)"
          : "None",
      };

      return {
        success: true,
        data: compactResult,
        message: `Found CVE ${cveId}`,
        note: "Response truncated for optimal performance. Full details available via direct NVD API query.",
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to retrieve CVE ${cveId}`,
      };
    }
  },

  async search_cves_by_keyword({ keyword, exactMatch = false, limit = 10 }) {
    try {
      // Limit results to prevent token overflow
      const actualLimit = Math.min(limit, 5);
      const result = await nvdClient.searchCVEsByKeyword(
        keyword,
        exactMatch,
        actualLimit
      );

      // Create compact vulnerability summaries
      const compactVulns =
        result.vulnerabilities?.map((vuln) => {
          const cve = vuln.cve;
          const description =
            cve.descriptions?.find((d) => d.lang === "en")?.value ||
            "No description";
          const cvssV3 = vuln.cve.metrics?.cvssMetricV3?.[0]?.cvssData;
          const cvssV2 = vuln.cve.metrics?.cvssMetricV2?.[0]?.cvssData;

          return {
            id: cve.id,
            description:
              description.length > 200
                ? description.substring(0, 200) + "..."
                : description,
            severity: cvssV3
              ? `${cvssV3.baseScore} (${cvssV3.baseSeverity})`
              : cvssV2
              ? `${cvssV2.baseScore} (${cvssV2.baseSeverity})`
              : "Not scored",
            published: cve.published,
          };
        }) || [];

      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: compactVulns,
          showing: compactVulns.length,
          limited: actualLimit < limit,
        },
        message: `Found ${result.totalResults} CVEs matching "${keyword}" (showing ${compactVulns.length})`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to search for "${keyword}"`,
      };
    }
  },

  async get_cves_by_severity({ severity, version = "v3", limit = 10 }) {
    try {
      const result = await nvdClient.getCVEsBySeverity(
        severity,
        version,
        limit
      );
      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: result.vulnerabilities,
        },
        message: `Found ${result.totalResults} CVEs with ${severity} severity (CVSS ${version})`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get CVEs with ${severity} severity`,
      };
    }
  },

  async get_recent_cves({ days = 7, limit = 10 }) {
    try {
      const result = await nvdClient.getRecentCVEs(days, limit);
      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: result.vulnerabilities,
        },
        message: `Found ${result.totalResults} CVEs published in the last ${days} days`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get recent CVEs`,
      };
    }
  },

  async get_cves_by_cpe({ cpeName, vulnerableOnly = false, limit = 10 }) {
    try {
      const result = await nvdClient.getCVEsByCPE(
        cpeName,
        vulnerableOnly,
        limit
      );
      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: result.vulnerabilities,
        },
        message: `Found ${result.totalResults} CVEs for ${cpeName}`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get CVEs for ${cpeName}`,
      };
    }
  },

  async get_cves_by_cwe({ cweId, limit = 10 }) {
    try {
      const result = await nvdClient.getCVEsByCWE(cweId, limit);
      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: result.vulnerabilities,
        },
        message: `Found ${result.totalResults} CVEs with weakness ${cweId}`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get CVEs for ${cweId}`,
      };
    }
  },

  async get_high_severity_kev_cves({ limit = 10 }) {
    try {
      const result = await nvdClient.getHighSeverityKEVCVEs(limit);
      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: result.vulnerabilities,
        },
        message: `Found ${result.totalResults} high-severity CVEs in CISA's KEV catalog`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get KEV CVEs`,
      };
    }
  },

  async get_cves_by_date_range({
    startDate,
    endDate,
    dateType = "published",
    limit = 10,
  }) {
    try {
      // Limit results to prevent token overflow
      const actualLimit = Math.min(limit, 5);
      const result = await nvdClient.getCVEsByDateRange(
        startDate,
        endDate,
        dateType,
        actualLimit
      );

      // Create compact vulnerability summaries
      const compactVulns =
        result.vulnerabilities?.map((vuln) => {
          const cve = vuln.cve;
          const description =
            cve.descriptions?.find((d) => d.lang === "en")?.value ||
            "No description";
          const cvssV3 = vuln.cve.metrics?.cvssMetricV3?.[0]?.cvssData;
          const cvssV2 = vuln.cve.metrics?.cvssMetricV2?.[0]?.cvssData;

          return {
            id: cve.id,
            description:
              description.length > 200
                ? description.substring(0, 200) + "..."
                : description,
            severity: cvssV3
              ? `${cvssV3.baseScore} (${cvssV3.baseSeverity})`
              : cvssV2
              ? `${cvssV2.baseScore} (${cvssV2.baseSeverity})`
              : "Not scored",
            published: cve.published,
            lastModified: cve.lastModified,
          };
        }) || [];

      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: compactVulns,
          showing: compactVulns.length,
          dateRange: { startDate, endDate, dateType },
          limited: actualLimit < limit,
        },
        message: `Found ${result.totalResults} CVEs ${dateType} between ${startDate} and ${endDate} (showing ${compactVulns.length})`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get CVEs for date range ${startDate} to ${endDate}`,
      };
    }
  },

  async get_cve_change_history({ cveId }) {
    try {
      const result = await nvdClient.getCVEChangeHistoryById(cveId);
      return {
        success: true,
        data: result,
        message: `Retrieved change history for ${cveId}`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get change history for ${cveId}`,
      };
    }
  },
};

/**
 * Format CVE data for display with token optimization
 * @param {Object} cve - CVE object from NVD API
 * @param {boolean} compact - Whether to use compact formatting
 * @returns {string} Formatted CVE information
 */
function formatCVEForDisplay(cve, compact = false) {
  if (!cve || !cve.cve) return "No CVE data available";

  const { cve: cveData } = cve;
  const description =
    cveData.descriptions?.find((d) => d.lang === "en")?.value ||
    "No description available";

  // Truncate description if too long
  const truncatedDescription =
    compact && description.length > 200
      ? description.substring(0, 200) + "..."
      : description;

  const cvssV3 = cve.cve.metrics?.cvssMetricV3?.[0]?.cvssData;
  const cvssV2 = cve.cve.metrics?.cvssMetricV2?.[0]?.cvssData;

  let formatted = `**${cveData.id}**\n`;
  formatted += `Description: ${truncatedDescription}\n`;

  if (!compact) {
    formatted += `Published: ${new Date(
      cveData.published
    ).toLocaleDateString()}\n`;
    formatted += `Last Modified: ${new Date(
      cveData.lastModified
    ).toLocaleDateString()}\n`;
  }

  if (cvssV3) {
    formatted += `CVSS v3.1 Score: ${cvssV3.baseScore} (${cvssV3.baseSeverity})\n`;
    if (!compact) {
      formatted += `Vector: ${cvssV3.vectorString}\n`;
    }
  } else if (cvssV2) {
    formatted += `CVSS v2 Score: ${cvssV2.baseScore} (${cvssV2.baseSeverity})\n`;
    if (!compact) {
      formatted += `Vector: ${cvssV2.vectorString}\n`;
    }
  }

  return formatted;
}

/**
 * Truncate function results to prevent token overflow
 * @param {Object} result - Function result object
 * @param {number} maxLength - Maximum length for result content
 * @returns {Object} Truncated result
 */
function truncateResult(result, maxLength = 2000) {
  if (!result || !result.data) return result;

  const resultString = JSON.stringify(result);
  if (resultString.length <= maxLength) return result;

  // If it's a vulnerability list, reduce the number of items
  if (
    result.data.vulnerabilities &&
    Array.isArray(result.data.vulnerabilities)
  ) {
    const maxItems = Math.min(3, result.data.vulnerabilities.length); // Max 3 items
    return {
      ...result,
      data: {
        ...result.data,
        vulnerabilities: result.data.vulnerabilities.slice(0, maxItems),
        truncated: true,
        originalCount: result.data.vulnerabilities.length,
      },
      message: `${result.message} (showing ${maxItems} of ${result.data.vulnerabilities.length} results due to size limits)`,
    };
  }

  // For single CVE or other data, aggressively truncate
  if (typeof result.data === "object") {
    const truncatedData = JSON.stringify(result.data).substring(0, maxLength);
    try {
      return {
        ...result,
        data: JSON.parse(truncatedData + "}"), // Try to close JSON properly
        truncated: true,
      };
    } catch {
      return {
        ...result,
        data: {
          summary: "Data truncated due to size",
          original_size: resultString.length,
        },
        truncated: true,
      };
    }
  }

  return {
    ...result,
    data:
      typeof result.data === "string"
        ? result.data.substring(0, maxLength) + "..."
        : result.data,
    truncated: true,
  };
}

/**
 * Estimate token count (rough approximation)
 * @param {string} text - Text to estimate
 * @returns {number} Estimated token count
 */
function estimateTokens(text) {
  return Math.ceil(text.length / 4); // Rough estimate: 1 token ≈ 4 characters
}

/**
 * Truncate conversation messages to fit within token limits
 * @param {Array} messages - Conversation messages
 * @param {number} maxTokens - Maximum tokens allowed
 * @returns {Array} Truncated messages
 */
function truncateConversation(messages, maxTokens = 6000) {
  let totalTokens = 0;
  const truncatedMessages = [];

  // Always keep the system message
  if (messages[0]?.role === "system") {
    truncatedMessages.push(messages[0]);
    totalTokens += estimateTokens(messages[0].content);
  }

  // Add messages from the end (most recent first) until we hit the limit
  for (
    let i = messages.length - 1;
    i >= (messages[0]?.role === "system" ? 1 : 0);
    i--
  ) {
    const message = messages[i];
    const messageTokens = estimateTokens(JSON.stringify(message));

    if (totalTokens + messageTokens > maxTokens) {
      break;
    }

    truncatedMessages.unshift(message);
    totalTokens += messageTokens;
  }

  return truncatedMessages;
}

/**
 * Helper function to handle function calls (both streaming and non-streaming)
 * @param {Object} message - Message with tool calls
 * @param {Array} conversationMessages - Previous conversation messages
 * @param {string} model - OpenAI model
 * @param {number} maxTokens - Max tokens
 * @param {boolean} stream - Whether to stream
 * @param {Function} onChunk - Streaming callback
 * @returns {Promise<Object>} Function call results
 */
async function handleFunctionCalls(
  message,
  conversationMessages,
  model,
  maxTokens,
  stream,
  onChunk
) {
  const functionResults = [];

  for (const toolCall of message.tool_calls) {
    const functionName = toolCall.function.name;
    const functionArgs = JSON.parse(toolCall.function.arguments);

    console.log(`Calling function: ${functionName} with args:`, functionArgs);

    if (onChunk) {
      onChunk({
        type: "function_call",
        function: functionName,
        arguments: functionArgs,
      });
    }

    if (functionImplementations[functionName]) {
      const result = await functionImplementations[functionName](functionArgs);
      const truncatedResult = truncateResult(result);
      functionResults.push({
        tool_call_id: toolCall.id,
        role: "tool",
        content: JSON.stringify(truncatedResult),
      });
    } else {
      functionResults.push({
        tool_call_id: toolCall.id,
        role: "tool",
        content: JSON.stringify({
          success: false,
          error: `Function ${functionName} not implemented`,
        }),
      });
    }
  }

  // Continue conversation with function results
  const followUpMessages = [
    ...conversationMessages,
    message,
    ...functionResults,
  ];
  const truncatedFollowUp = truncateConversation(followUpMessages, 4000);

  const followUpResponse = await openai.chat.completions.create({
    model,
    messages: truncatedFollowUp,
    max_tokens: Math.min(maxTokens, 1000),
    temperature: 0.1,
    stream: stream,
  });

  if (stream) {
    let fullFollowUpContent = "";

    for await (const chunk of followUpResponse) {
      const delta = chunk.choices[0]?.delta;
      if (delta?.content) {
        fullFollowUpContent += delta.content;
        if (onChunk) {
          onChunk({ type: "content", content: delta.content });
        }
      }
    }

    return {
      success: true,
      response: fullFollowUpContent,
      functionCalls: message.tool_calls.map((tc) => ({
        function: tc.function.name,
        arguments: JSON.parse(tc.function.arguments),
      })),
      usage: null,
    };
  }

  return {
    success: true,
    response: followUpResponse.choices[0].message.content,
    functionCalls: message.tool_calls.map((tc) => ({
      function: tc.function.name,
      arguments: JSON.parse(tc.function.arguments),
    })),
    usage: {
      initial: null,
      followUp: followUpResponse.usage,
    },
  };
}

/**
 * Main function to handle NVD chat conversations
 * @param {Array} messages - Array of conversation messages
 * @param {Object} options - Additional options
 * @param {string} [options.model='gpt-4'] - OpenAI model to use
 * @param {number} [options.maxTokens=2000] - Maximum tokens for response
 * @param {boolean} [options.stream=false] - Whether to stream the response
 * @param {Function} [options.onChunk] - Callback for streaming chunks
 * @returns {Promise<Object>} Response from the assistant
 */
async function handleNVDChat(messages, options = {}) {
  const {
    model = "gpt-4",
    maxTokens = 2000,
    stream = false,
    onChunk,
  } = options;

  try {
    // Add system message if not present
    const systemMessage = {
      role: "system",
      content: `You are a cybersecurity assistant for NVD queries. Present CVE info with ID, description, severity, impact, and remediation. Be concise.`,
    };

    const conversationMessages =
      messages[0]?.role === "system" ? messages : [systemMessage, ...messages];

    // Truncate conversation to prevent token overflow
    const truncatedMessages = truncateConversation(conversationMessages, 5000);

    const response = await openai.chat.completions.create({
      model,
      messages: truncatedMessages,
      tools: nvdTools,
      tool_choice: "auto",
      max_tokens: maxTokens,
      temperature: 0.1,
      stream: stream,
    });

    // Handle streaming response
    if (stream) {
      let fullContent = "";
      let toolCalls = [];

      for await (const chunk of response) {
        const delta = chunk.choices[0]?.delta;

        if (delta?.content) {
          fullContent += delta.content;
          if (onChunk) {
            onChunk({ type: "content", content: delta.content });
          }
        }

        if (delta?.tool_calls) {
          for (const toolCall of delta.tool_calls) {
            if (toolCall.index !== undefined) {
              if (!toolCalls[toolCall.index]) {
                toolCalls[toolCall.index] = {
                  id: toolCall.id || "",
                  type: "function",
                  function: { name: "", arguments: "" },
                };
              }

              if (toolCall.function?.name) {
                toolCalls[toolCall.index].function.name +=
                  toolCall.function.name;
              }
              if (toolCall.function?.arguments) {
                toolCalls[toolCall.index].function.arguments +=
                  toolCall.function.arguments;
              }
            }
          }
        }
      }

      // Create message object for function handling
      const message = {
        content: fullContent,
        tool_calls: toolCalls.length > 0 ? toolCalls : undefined,
      };

      // Handle function calls if present
      if (message.tool_calls) {
        return await handleFunctionCalls(
          message,
          truncatedMessages,
          model,
          maxTokens,
          stream,
          onChunk
        );
      }

      return {
        success: true,
        response: fullContent,
        functionCalls: [],
        usage: null, // Usage not available in streaming
      };
    }

    const message = response.choices[0].message;

    // Handle function calls
    if (message.tool_calls) {
      return await handleFunctionCalls(
        message,
        truncatedMessages,
        model,
        maxTokens,
        stream,
        onChunk
      );
    }

    return {
      success: true,
      response: message.content,
      functionCalls: [],
      usage: response.usage,
    };
  } catch (error) {
    console.error("Error in NVD chat handler:", error);
    return {
      success: false,
      error: error.message,
      response:
        "I encountered an error while processing your request. Please try again.",
    };
  }
}

/**
 * Convenience function for single message queries
 * @param {string} userMessage - User's message/query
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Response from the assistant
 */
async function queryNVD(userMessage, options = {}) {
  const messages = [{ role: "user", content: userMessage }];
  return handleNVDChat(messages, options);
}

export default {
  handleNVDChat,
  queryNVD,
  nvdTools,
  functionImplementations,
  formatCVEForDisplay,
};

// Example usage:
/*
const nvdChat = require('./nvd-chat-assistant');

// Single query
const result = await nvdChat.queryNVD("What are the latest critical vulnerabilities?");
console.log(result.response);

// Conversation
const messages = [
  { role: "user", content: "Tell me about CVE-2021-44228" },
  { role: "assistant", content: "Let me look that up for you..." },
  { role: "user", content: "What's the severity and how can I fix it?" }
];

const conversation = await nvdChat.handleNVDChat(messages);
console.log(conversation.response);
*/
